import React, { useState, useEffect } from 'react';
import { apiGet } from '../utils/apiUtils';
import ErrorDisplay from './ErrorDisplay';
import TreeView from './TreeView';
import Statistics from './Statistics';

const FileList = ({ scanSession, onFilesSelected, selectedFiles, onProceedToMigration }) => {
    const [tree, setTree] = useState([]);
    const [stats, setStats] = useState(null);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState(null);
    const [viewMode, setViewMode] = useState('tree'); // 'tree' or 'table'
    const [filters, setFilters] = useState({
        search: '',
        mimeType: 'all',
        downloadStatus: 'all'
    });

    useEffect(() => {
        if (scanSession?.id) {
            loadTreeData();
        }
    }, [scanSession, filters]);



    const loadTreeData = async () => {
        if (!scanSession?.id) {
            console.log('No scan session ID available');
            return;
        }

        console.log('Loading tree data for session:', scanSession.id);
        setLoading(true);
        setError(null);

        try {
            const queryParams = new URLSearchParams({
                sessionId: scanSession.id,
                ...filters
            });

            console.log('Calling API:', `/api/scan/files/tree?${queryParams}`);
            const data = await apiGet(`/api/scan/files/tree?${queryParams}`);
            console.log('Tree data received:', data);

            setTree(data.tree || []);
            setStats(data.stats || null);

        } catch (err) {
            console.error('Error loading tree data:', err);
            setError(err);
            setTree([]);
            setStats(null);
        } finally {
            setLoading(false);
        }
    };

    const handleFileSelect = (file, isSelected) => {
        let newSelection;

        if (isSelected) {
            newSelection = [...selectedFiles, file];
        } else {
            newSelection = selectedFiles.filter(f => f.id !== file.id);
        }

        onFilesSelected(newSelection);
    };

    const handleSelectAll = (allFiles) => {
        onFilesSelected(allFiles);
    };

    const handleFilterChange = (filterName, value) => {
        setFilters(prev => ({
            ...prev,
            [filterName]: value
        }));
    };



    if (loading) {
        return (
            <div className="file-list">
                <div className="loading-state">
                    <div className="spinner"></div>
                    <p>Loading files...</p>
                </div>
            </div>
        );
    }

    if (error) {
        return (
            <div className="file-list">
                <ErrorDisplay
                    error={error}
                    title="Lỗi tải danh sách file"
                    onRetry={loadTreeData}
                    onDismiss={() => setError(null)}
                />
            </div>
        );
    }

    return (
        <div className="file-list">
            <h2>📋 Select Files to Migrate</h2>

            {/* Statistics */}
            <Statistics stats={stats} selectedFiles={selectedFiles} />

            <div className="file-filters">
                <div className="filter-row">
                    <input
                        type="text"
                        placeholder="Search files..."
                        value={filters.search}
                        onChange={(e) => handleFilterChange('search', e.target.value)}
                        className="search-input"
                    />

                    <select
                        value={filters.mimeType}
                        onChange={(e) => handleFilterChange('mimeType', e.target.value)}
                        className="filter-select"
                    >
                        <option value="all">All File Types</option>
                        <option value="application/vnd.google-apps.document">Google Docs</option>
                        <option value="application/vnd.google-apps.spreadsheet">Google Sheets</option>
                        <option value="application/vnd.google-apps.presentation">Google Slides</option>
                        <option value="application/pdf">PDF Files</option>
                        <option value="image/">Images</option>
                        <option value="application/vnd.google-apps.folder">Folders</option>
                    </select>

                    <select
                        value={filters.downloadStatus}
                        onChange={(e) => handleFilterChange('downloadStatus', e.target.value)}
                        className="filter-select"
                    >
                        <option value="all">All Download Status</option>
                        <option value="not_downloaded">Not Downloaded</option>
                        <option value="downloaded">Downloaded</option>
                        <option value="failed">Failed</option>
                    </select>
                </div>
            </div>

            {/* Tree View */}
            <TreeView
                tree={tree}
                selectedFiles={selectedFiles}
                onFileSelect={handleFileSelect}
                onSelectAll={handleSelectAll}
            />

            <div className="action-buttons">
                <button
                    onClick={onProceedToMigration}
                    disabled={selectedFiles.length === 0}
                    className="btn btn-primary btn-large"
                >
                    Continue with {selectedFiles.length} Selected Files
                </button>
            </div>
        </div>
    );
};

export default FileList;
