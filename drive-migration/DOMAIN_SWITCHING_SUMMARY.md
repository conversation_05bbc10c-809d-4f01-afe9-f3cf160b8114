# Domain Switching Implementation Summary

This document summarizes the changes made to support switching between domains (osp.vn or osp.com.vn), Google service account JSON file paths, and admin accounts through environment variables.

## Changes Made

1. **Updated `GoogleAuth` Class**
   - Added support for reading domain, service account path, and admin email from environment variables
   - Added helper methods to get domain and admin email
   - Updated methods to use the domain from environment variables

2. **Created Template `.env` Files**
   - `.env.osp.vn` - Template for osp.vn domain
   - `.env.osp.com.vn` - Template for osp.com.vn domain

3. **Created Domain Switching Script**
   - `switch-domain.sh` - Script to switch between domains
   - Usage: `./switch-domain.sh [osp.vn|osp.com.vn]`

4. **Created Test Script**
   - `test-domain-config.js` - Script to test the domain configuration
   - Tests connection with admin email and domain-wide delegation

5. **Updated Existing Test Files**
   - Updated `comprehensive-auth-test.js` to use the new configuration

6. **Created Documentation**
   - `DOMAIN_CONFIGURATION.md` - Documentation for domain configuration
   - Explains how to use the new configuration options

## Environment Variables

The following environment variables are used to configure the domain, Google service account, and admin account:

| Variable | Description | Default |
|----------|-------------|---------|
| `GOOGLE_DOMAIN` | The domain to use (osp.vn or osp.com.vn) | osp.com.vn |
| `GOOGLE_SERVICE_ACCOUNT_PATH` | Path to the Google service account JSON file | ./google-service-account.json |
| `GOOGLE_ADMIN_EMAIL` | Email of the admin account | admin@{GOOGLE_DOMAIN} |

## How to Switch Domains

### Using the Script

```bash
# Switch to osp.vn domain
./switch-domain.sh osp.vn

# Switch to osp.com.vn domain
./switch-domain.sh osp.com.vn
```

### Manually

1. Edit the `.env` file directly and update the `GOOGLE_DOMAIN`, `GOOGLE_SERVICE_ACCOUNT_PATH`, and `GOOGLE_ADMIN_EMAIL` variables.

2. Or copy one of the template files:

```bash
# For osp.vn domain
cp .env.osp.vn .env

# For osp.com.vn domain
cp .env.osp.com.vn .env
```

## Testing the Configuration

To test the domain configuration, run:

```bash
node test-domain-config.js
```

This script will:
1. Display the current configuration
2. Test connection with admin email
3. Test domain-wide delegation validation

## Service Account Files

The application expects the Google service account JSON files to be in the root directory:

- `google-service-account.json` - Default service account file
- `google-service-account-osp-vn.json` - Service account for osp.vn domain
- `google-service-account-osp-com-vn.json` - Service account for osp.com.vn domain

You can specify a different path using the `GOOGLE_SERVICE_ACCOUNT_PATH` environment variable.

## Troubleshooting

If you encounter issues with the domain configuration:

1. Check that the Google service account JSON file exists and is valid
2. Verify that the admin account has the necessary permissions
3. Ensure that the domain is correctly configured in the Google Admin Console
4. Check the application logs for any error messages

For more information, see the [Domain Configuration Guide](./DOMAIN_CONFIGURATION.md) and [Domain-Wide Delegation Setup](./docs/DOMAIN_WIDE_DELEGATION_SETUP.md) documentation.
