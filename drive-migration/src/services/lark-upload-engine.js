import { larkDriveAPI } from '../api/lark-drive-api.js';
import fs from 'fs';
import path from 'path';
import { createReadStream } from 'fs';

/**
 * Lark Upload Engine
 * Advanced engine để upload files lên Lark Drive với support cho:
 * - Smart upload (auto-detect small vs large files)
 * - Multipart chunked uploads cho large files
 * - Progress tracking và resume capability
 * - Error handling và retry logic
 * - Concurrent uploads với rate limiting
 * - Folder structure recreation
 */
export class LarkUploadEngine {
    constructor() {
        this.larkAPI = larkDriveAPI;
        
        // Configuration
        this.config = {
            maxConcurrentUploads: 3, // Lower than downloads để tránh overwhelm Lark API
            chunkSize: 10 * 1024 * 1024, // 10MB chunks
            maxRetries: 3,
            retryDelay: 2000, // 2 seconds (longer than download)
            maxFileSize: 15 * 1024 * 1024 * 1024, // 15GB limit
            smallFileThreshold: 20 * 1024 * 1024, // 20MB threshold
            supportedMimeTypes: [
                // Documents
                'application/pdf',
                'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
                'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
                'application/vnd.openxmlformats-officedocument.presentationml.presentation',
                'application/msword',
                'application/vnd.ms-excel',
                'application/vnd.ms-powerpoint',
                // Images
                'image/jpeg',
                'image/png',
                'image/gif',
                'image/bmp',
                'image/webp',
                // Text
                'text/plain',
                'text/csv',
                'text/html',
                // Archives
                'application/zip',
                'application/x-rar-compressed',
                'application/x-7z-compressed',
                // Media
                'video/mp4',
                'video/avi',
                'video/mov',
                'audio/mp3',
                'audio/wav',
                'audio/flac'
            ]
        };

        // Statistics tracking
        this.stats = {
            totalUploads: 0,
            successfulUploads: 0,
            failedUploads: 0,
            bytesUploaded: 0,
            averageSpeed: 0,
            activeUploads: 0,
            foldersCreated: 0,
            errors: []
        };

        // Active uploads tracking
        this.activeUploads = new Map();
        this.uploadQueue = [];
        this.folderCache = new Map(); // Cache folder structure
    }

    /**
     * Upload single file với comprehensive error handling
     * @param {string} filePath - Path to local file
     * @param {string} targetFileName - Target file name in Lark
     * @param {string} parentFolderId - Parent folder ID in Lark
     * @param {function} progressCallback - Progress callback function
     * @returns {Promise<object>} Upload result với file token và metadata
     */
    async uploadFile(filePath, targetFileName, parentFolderId = null, progressCallback = null) {
        const uploadId = `${path.basename(filePath)}_${Date.now()}`;
        
        try {
            this.stats.totalUploads++;
            this.stats.activeUploads++;
            
            console.log(`📤 Starting upload: ${targetFileName} to Lark Drive`);
            
            // Validate file exists
            if (!fs.existsSync(filePath)) {
                throw new Error(`File not found: ${filePath}`);
            }

            // Get file info
            const fileStats = fs.statSync(filePath);
            const fileSize = fileStats.size;
            
            // Validate file size
            if (fileSize > this.config.maxFileSize) {
                throw new Error(`File too large: ${this.formatFileSize(fileSize)} (max: ${this.formatFileSize(this.config.maxFileSize)})`);
            }

            // Validate MIME type
            const mimeType = this.getMimeType(targetFileName);
            if (!this.isSupportedMimeType(mimeType)) {
                console.warn(`⚠️ Unsupported MIME type: ${mimeType} for ${targetFileName}`);
            }

            // Read file content
            const fileContent = fs.readFileSync(filePath);
            
            // Create progress wrapper
            const wrappedProgressCallback = progressCallback ? (progress) => {
                progressCallback({
                    uploadId,
                    fileName: targetFileName,
                    filePath,
                    ...progress
                });
            } : null;

            // Use Lark API smart upload
            const uploadResult = await this.larkAPI.uploadFile(
                fileContent,
                targetFileName,
                parentFolderId,
                wrappedProgressCallback
            );

            this.stats.successfulUploads++;
            this.stats.bytesUploaded += fileSize;
            
            console.log(`✅ Upload completed: ${targetFileName} -> ${uploadResult.file_token}`);
            
            return {
                success: true,
                uploadId,
                fileName: targetFileName,
                filePath,
                fileToken: uploadResult.file_token,
                fileSize,
                mimeType,
                parentFolderId,
                uploadTime: uploadResult.uploadTime || 0,
                uploadedAt: uploadResult.uploadedAt || new Date().toISOString(),
                larkResult: uploadResult
            };

        } catch (error) {
            this.stats.failedUploads++;
            this.stats.errors.push({
                fileName: targetFileName,
                filePath,
                error: error.message,
                timestamp: new Date().toISOString()
            });
            
            console.error(`❌ Upload failed: ${targetFileName} - ${error.message}`);
            
            return {
                success: false,
                uploadId,
                fileName: targetFileName,
                filePath,
                error: error.message,
                timestamp: new Date().toISOString()
            };
        } finally {
            this.stats.activeUploads--;
            this.activeUploads.delete(uploadId);
        }
    }

    /**
     * Upload multiple files với concurrent processing
     * @param {Array} fileList - Array of {filePath, targetFileName, parentFolderId}
     * @param {function} progressCallback - Progress callback for all files
     * @returns {Promise<Array>} Array of upload results
     */
    async uploadMultipleFiles(fileList, progressCallback = null) {
        console.log(`📤 Starting batch upload: ${fileList.length} files`);
        
        const results = [];
        const totalFiles = fileList.length;
        let completedFiles = 0;

        // Process files in batches to respect concurrent limit
        for (let i = 0; i < fileList.length; i += this.config.maxConcurrentUploads) {
            const batch = fileList.slice(i, i + this.config.maxConcurrentUploads);
            
            const batchPromises = batch.map(async (fileInfo) => {
                const result = await this.uploadFile(
                    fileInfo.filePath,
                    fileInfo.targetFileName,
                    fileInfo.parentFolderId,
                    (progress) => {
                        if (progressCallback) {
                            progressCallback({
                                type: 'file_progress',
                                fileName: fileInfo.targetFileName,
                                ...progress
                            });
                        }
                    }
                );
                
                completedFiles++;
                
                if (progressCallback) {
                    progressCallback({
                        type: 'batch_progress',
                        completedFiles,
                        totalFiles,
                        progress: (completedFiles / totalFiles) * 100,
                        currentFile: fileInfo.targetFileName,
                        result
                    });
                }
                
                return result;
            });

            const batchResults = await Promise.all(batchPromises);
            results.push(...batchResults);
            
            // Small delay between batches
            if (i + this.config.maxConcurrentUploads < fileList.length) {
                await new Promise(resolve => setTimeout(resolve, 1000));
            }
        }

        const successCount = results.filter(r => r.success).length;
        const failureCount = results.filter(r => !r.success).length;
        
        console.log(`✅ Batch upload completed: ${successCount} success, ${failureCount} failed`);
        
        return results;
    }

    /**
     * Create folder structure in Lark Drive
     * @param {string} folderPath - Folder path (e.g., "Documents/Projects/2025")
     * @param {string} parentFolderId - Parent folder ID
     * @returns {Promise<object>} Created folder info
     */
    async createFolderStructure(folderPath, parentFolderId = null) {
        const cacheKey = `${parentFolderId || 'root'}:${folderPath}`;
        
        // Check cache first
        if (this.folderCache.has(cacheKey)) {
            console.log(`📁 Using cached folder: ${folderPath}`);
            return this.folderCache.get(cacheKey);
        }

        try {
            console.log(`📁 Creating folder structure: ${folderPath}`);
            
            const pathParts = folderPath.split('/').filter(part => part.trim());
            let currentParentId = parentFolderId;
            let currentPath = '';
            
            for (const folderName of pathParts) {
                currentPath = currentPath ? `${currentPath}/${folderName}` : folderName;
                const folderCacheKey = `${currentParentId || 'root'}:${currentPath}`;
                
                // Check if this level is cached
                if (this.folderCache.has(folderCacheKey)) {
                    const cachedFolder = this.folderCache.get(folderCacheKey);
                    currentParentId = cachedFolder.token;
                    continue;
                }
                
                // Create folder using Lark API
                const folderResult = await this.larkAPI.createFolder(folderName, currentParentId);
                
                // Cache the result
                this.folderCache.set(folderCacheKey, folderResult);
                currentParentId = folderResult.token;
                
                this.stats.foldersCreated++;
                console.log(`✅ Created folder: ${folderName} -> ${folderResult.token}`);
            }

            const finalResult = {
                path: folderPath,
                token: currentParentId,
                parentId: parentFolderId,
                createdAt: new Date().toISOString()
            };

            // Cache the final result
            this.folderCache.set(cacheKey, finalResult);
            
            return finalResult;

        } catch (error) {
            console.error(`❌ Error creating folder structure ${folderPath}:`, error.message);
            throw new Error(`Failed to create folder structure: ${error.message}`);
        }
    }

    /**
     * Get MIME type from file extension
     * @param {string} fileName - File name
     * @returns {string} MIME type
     */
    getMimeType(fileName) {
        const ext = path.extname(fileName).toLowerCase();
        const mimeTypes = {
            '.pdf': 'application/pdf',
            '.docx': 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
            '.xlsx': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
            '.pptx': 'application/vnd.openxmlformats-officedocument.presentationml.presentation',
            '.doc': 'application/msword',
            '.xls': 'application/vnd.ms-excel',
            '.ppt': 'application/vnd.ms-powerpoint',
            '.jpg': 'image/jpeg',
            '.jpeg': 'image/jpeg',
            '.png': 'image/png',
            '.gif': 'image/gif',
            '.bmp': 'image/bmp',
            '.webp': 'image/webp',
            '.txt': 'text/plain',
            '.csv': 'text/csv',
            '.html': 'text/html',
            '.zip': 'application/zip',
            '.rar': 'application/x-rar-compressed',
            '.7z': 'application/x-7z-compressed',
            '.mp4': 'video/mp4',
            '.avi': 'video/avi',
            '.mov': 'video/mov',
            '.mp3': 'audio/mp3',
            '.wav': 'audio/wav',
            '.flac': 'audio/flac'
        };
        
        return mimeTypes[ext] || 'application/octet-stream';
    }

    /**
     * Check if MIME type is supported
     * @param {string} mimeType - MIME type to check
     * @returns {boolean} Is supported
     */
    isSupportedMimeType(mimeType) {
        return this.config.supportedMimeTypes.includes(mimeType);
    }

    /**
     * Format file size for display
     * @param {number} bytes - File size in bytes
     * @returns {string} Formatted size
     */
    formatFileSize(bytes) {
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }

    /**
     * Get upload statistics
     * @returns {object} Current statistics
     */
    getStats() {
        return {
            ...this.stats,
            successRate: this.stats.totalUploads > 0 
                ? (this.stats.successfulUploads / this.stats.totalUploads) * 100 
                : 0,
            averageFileSize: this.stats.successfulUploads > 0 
                ? this.stats.bytesUploaded / this.stats.successfulUploads 
                : 0,
            totalSizeFormatted: this.formatFileSize(this.stats.bytesUploaded)
        };
    }

    /**
     * Clear folder cache
     */
    clearFolderCache() {
        this.folderCache.clear();
        console.log('🗑️ Folder cache cleared');
    }

    /**
     * Get active uploads info
     * @returns {Array} Active upload information
     */
    getActiveUploads() {
        return Array.from(this.activeUploads.entries()).map(([id, info]) => ({
            uploadId: id,
            ...info
        }));
    }
}

// Export singleton instance
export const larkUploadEngine = new LarkUploadEngine();
