import express from 'express';
import { migrationEngine } from '../services/migration-engine.js';
import { realtimeService } from '../services/realtime-service.js';
import { supabaseClient } from '../database/supabase.js';

const router = express.Router();

/**
 * Start migration process
 * POST /api/migration/start
 */
router.post('/start', async (req, res) => {
    try {
        const { userEmail, sessionId, options = {} } = req.body;

        if (!userEmail || !sessionId) {
            return res.status(400).json({
                error: 'userEmail and sessionId are required'
            });
        }

        console.log(`🚀 Starting migration for session: ${sessionId}`);

        // Generate migration ID
        const migrationId = `migration_${sessionId}_${Date.now()}`;

        // Start migration with progress tracking
        const migrationPromise = migrationEngine.startMigration(
            userEmail,
            sessionId,
            options,
            (progress) => {
                // Progress is now handled via real-time updates
                console.log(`📊 Migration progress:`, progress.type || 'update');
            }
        );

        // Return immediately with migration ID
        // The actual migration runs in background

        res.json({
            success: true,
            message: 'Migration started successfully',
            migrationId,
            status: 'running'
        });

        // Wait for migration to complete (in background)
        try {
            const result = await migrationPromise;
            console.log(`✅ Migration completed: ${migrationId}`, result);
        } catch (error) {
            console.error(`❌ Migration failed: ${migrationId}`, error.message);
        }

    } catch (error) {
        console.error('❌ Error starting migration:', error.message);
        res.status(500).json({
            error: 'Failed to start migration',
            details: error.message
        });
    }
});

/**
 * Get migration status
 * GET /api/migration/status/:migrationId
 */
router.get('/status/:migrationId', async (req, res) => {
    try {
        const { migrationId } = req.params;

        const { data: migration, error } = await supabaseClient
            .from('migration_tasks')
            .select('*')
            .eq('id', migrationId)
            .single();

        if (error) {
            throw new Error(`Database error: ${error.message}`);
        }

        if (!migration) {
            return res.status(404).json({
                error: 'Migration not found'
            });
        }

        // Get migration items summary
        const { data: items, error: itemsError } = await supabaseClient
            .from('migration_items')
            .select('status, error_message')
            .eq('migration_task_id', migrationId);

        if (itemsError) {
            console.warn('Warning: Could not fetch migration items:', itemsError.message);
        }

        const itemsSummary = items ? {
            total: items.length,
            completed: items.filter(i => i.status === 'completed').length,
            failed: items.filter(i => i.status === 'failed').length,
            pending: items.filter(i => i.status === 'pending').length
        } : null;

        res.json({
            migration,
            itemsSummary,
            progress: migration.total_files > 0
                ? (migration.processed_files / migration.total_files) * 100
                : 0
        });

    } catch (error) {
        console.error('❌ Error getting migration status:', error.message);
        res.status(500).json({
            error: 'Failed to get migration status',
            details: error.message
        });
    }
});

/**
 * Get migration items với pagination
 * GET /api/migration/items/:migrationId
 */
router.get('/items/:migrationId', async (req, res) => {
    try {
        const { migrationId } = req.params;
        const {
            page = 1,
            pageSize = 50,
            status = 'all', // 'all', 'completed', 'failed', 'pending'
            search = ''
        } = req.query;

        // Build query
        let query = supabaseClient
            .from('migration_items')
            .select('*', { count: 'exact' })
            .eq('migration_task_id', migrationId);

        // Apply filters
        if (status !== 'all') {
            query = query.eq('status', status);
        }

        if (search) {
            query = query.ilike('google_file_name', `%${search}%`);
        }

        // Apply sorting
        query = query.order('created_at', { ascending: false });

        // Apply pagination
        const offset = (parseInt(page) - 1) * parseInt(pageSize);
        query = query.range(offset, offset + parseInt(pageSize) - 1);

        const { data: items, error, count } = await query;

        if (error) {
            throw new Error(`Database error: ${error.message}`);
        }

        const totalPages = Math.ceil((count || 0) / parseInt(pageSize));

        res.json({
            items: items || [],
            pagination: {
                currentPage: parseInt(page),
                pageSize: parseInt(pageSize),
                totalCount: count || 0,
                totalPages,
                hasNextPage: parseInt(page) < totalPages,
                hasPreviousPage: parseInt(page) > 1
            },
            filters: {
                status,
                search
            }
        });

    } catch (error) {
        console.error('❌ Error getting migration items:', error.message);
        res.status(500).json({
            error: 'Failed to get migration items',
            details: error.message
        });
    }
});

/**
 * Get all migrations for a user
 * GET /api/migration/list
 */
router.get('/list', async (req, res) => {
    try {
        const {
            userEmail,
            page = 1,
            pageSize = 20,
            status = 'all'
        } = req.query;

        if (!userEmail) {
            return res.status(400).json({
                error: 'userEmail is required'
            });
        }

        // Build query
        let query = supabaseClient
            .from('migration_tasks')
            .select('*', { count: 'exact' })
            .eq('user_email', userEmail);

        // Apply status filter
        if (status !== 'all') {
            query = query.eq('status', status);
        }

        // Apply sorting
        query = query.order('created_at', { ascending: false });

        // Apply pagination
        const offset = (parseInt(page) - 1) * parseInt(pageSize);
        query = query.range(offset, offset + parseInt(pageSize) - 1);

        const { data: migrations, error, count } = await query;

        if (error) {
            throw new Error(`Database error: ${error.message}`);
        }

        const totalPages = Math.ceil((count || 0) / parseInt(pageSize));

        res.json({
            migrations: migrations || [],
            pagination: {
                currentPage: parseInt(page),
                pageSize: parseInt(pageSize),
                totalCount: count || 0,
                totalPages,
                hasNextPage: parseInt(page) < totalPages,
                hasPreviousPage: parseInt(page) > 1
            }
        });

    } catch (error) {
        console.error('❌ Error getting migrations list:', error.message);
        res.status(500).json({
            error: 'Failed to get migrations list',
            details: error.message
        });
    }
});

/**
 * Cancel migration
 * POST /api/migration/cancel/:migrationId
 */
router.post('/cancel/:migrationId', async (req, res) => {
    try {
        const { migrationId } = req.params;

        console.log(`🛑 Cancelling migration: ${migrationId}`);

        // Update migration status to cancelled
        const { data, error } = await supabaseClient
            .from('migration_tasks')
            .update({
                status: 'cancelled',
                completed_at: new Date().toISOString(),
                updated_at: new Date().toISOString()
            })
            .eq('id', migrationId)
            .select()
            .single();

        if (error) {
            throw new Error(`Database error: ${error.message}`);
        }

        if (!data) {
            return res.status(404).json({
                error: 'Migration not found'
            });
        }

        res.json({
            success: true,
            message: 'Migration cancelled successfully',
            migration: data
        });

    } catch (error) {
        console.error('❌ Error cancelling migration:', error.message);
        res.status(500).json({
            error: 'Failed to cancel migration',
            details: error.message
        });
    }
});

/**
 * Retry failed migration items
 * POST /api/migration/retry/:migrationId
 */
router.post('/retry/:migrationId', async (req, res) => {
    try {
        const { migrationId } = req.params;
        const { itemIds = [] } = req.body;

        console.log(`🔄 Retrying migration items: ${migrationId}`);

        // Get failed items to retry
        let query = supabaseClient
            .from('migration_items')
            .select('*')
            .eq('migration_task_id', migrationId)
            .eq('status', 'failed');

        if (itemIds.length > 0) {
            query = query.in('id', itemIds);
        }

        const { data: failedItems, error } = await query;

        if (error) {
            throw new Error(`Database error: ${error.message}`);
        }

        if (!failedItems || failedItems.length === 0) {
            return res.json({
                success: true,
                message: 'No failed items to retry',
                retriedCount: 0
            });
        }

        // Reset failed items to pending
        const { error: updateError } = await supabaseClient
            .from('migration_items')
            .update({
                status: 'pending',
                error_message: null,
                retries: 0,
                updated_at: new Date().toISOString()
            })
            .in('id', failedItems.map(item => item.id));

        if (updateError) {
            throw new Error(`Database error: ${updateError.message}`);
        }

        // TODO: Restart migration for these items
        // This would require integration with the migration engine

        res.json({
            success: true,
            message: `${failedItems.length} items marked for retry`,
            retriedCount: failedItems.length
        });

    } catch (error) {
        console.error('❌ Error retrying migration:', error.message);
        res.status(500).json({
            error: 'Failed to retry migration',
            details: error.message
        });
    }
});

/**
 * Get realtime channel info
 * GET /api/migration/realtime/:migrationId
 */
router.get('/realtime/:migrationId', async (req, res) => {
    try {
        const { migrationId } = req.params;

        // Get channel presence
        const presence = realtimeService.getChannelPresence(migrationId);

        // Get realtime service stats
        const stats = realtimeService.getStats();

        res.json({
            success: true,
            migrationId,
            presence,
            realtimeStats: stats,
            channelName: `migration_${migrationId}`
        });

    } catch (error) {
        console.error('❌ Error getting realtime info:', error.message);
        res.status(500).json({
            error: 'Failed to get realtime info',
            details: error.message
        });
    }
});

/**
 * Get migration statistics
 * GET /api/migration/stats
 */
router.get('/stats', async (req, res) => {
    try {
        const { userEmail } = req.query;

        if (!userEmail) {
            return res.status(400).json({
                error: 'userEmail is required'
            });
        }

        // Get migration statistics
        const { data: migrations, error } = await supabaseClient
            .from('migration_tasks')
            .select('status, total_files, successful_files, failed_files, total_size, processed_size')
            .eq('user_email', userEmail);

        if (error) {
            throw new Error(`Database error: ${error.message}`);
        }

        const stats = {
            totalMigrations: migrations?.length || 0,
            completedMigrations: migrations?.filter(m => m.status === 'completed').length || 0,
            failedMigrations: migrations?.filter(m => m.status === 'failed').length || 0,
            runningMigrations: migrations?.filter(m => m.status === 'running').length || 0,
            totalFiles: migrations?.reduce((sum, m) => sum + (m.total_files || 0), 0) || 0,
            successfulFiles: migrations?.reduce((sum, m) => sum + (m.successful_files || 0), 0) || 0,
            failedFiles: migrations?.reduce((sum, m) => sum + (m.failed_files || 0), 0) || 0,
            totalSize: migrations?.reduce((sum, m) => sum + (m.total_size || 0), 0) || 0,
            processedSize: migrations?.reduce((sum, m) => sum + (m.processed_size || 0), 0) || 0
        };

        // Calculate rates
        stats.successRate = stats.totalFiles > 0
            ? (stats.successfulFiles / stats.totalFiles) * 100
            : 0;

        stats.completionRate = stats.totalMigrations > 0
            ? (stats.completedMigrations / stats.totalMigrations) * 100
            : 0;

        res.json({
            success: true,
            stats
        });

    } catch (error) {
        console.error('❌ Error getting migration stats:', error.message);
        res.status(500).json({
            error: 'Failed to get migration statistics',
            details: error.message
        });
    }
});

export default router;
