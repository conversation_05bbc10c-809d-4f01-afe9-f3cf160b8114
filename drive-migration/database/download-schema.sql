-- Download Feature Database Schema
-- T<PERSON><PERSON> c<PERSON><PERSON> bảng cần thiết cho tính năng download files từ Google Drive

-- Bảng download_sessions: <PERSON>u<PERSON><PERSON> lý các session download
CREATE TABLE IF NOT EXISTS download_sessions (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  name TEXT NOT NULL,
  selected_users TEXT[] NOT NULL, -- Array of user emails
  download_path TEXT NOT NULL, -- Local path để download
  concurrent_downloads INTEGER DEFAULT 3,
  max_retries INTEGER DEFAULT 3,
  status TEXT DEFAULT 'pending' CHECK (status IN ('pending', 'running', 'paused', 'completed', 'failed', 'cancelled')),
  total_files INTEGER DEFAULT 0,
  downloaded_files INTEGER DEFAULT 0,
  failed_files INTEGER DEFAULT 0,
  skipped_files INTEGER DEFAULT 0,
  total_size BIGINT DEFAULT 0,
  downloaded_size BIGINT DEFAULT 0,
  current_user_email TEXT, -- User hiện tại đang download
  current_file_name TEXT, -- File hiện tại đang download
  error_message TEXT,
  started_at TIMESTAMPTZ,
  completed_at TIMESTAMPTZ,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Bảng download_items: Track từng file download
CREATE TABLE IF NOT EXISTS download_items (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  download_session_id UUID NOT NULL REFERENCES download_sessions(id) ON DELETE CASCADE,
  scanned_file_id UUID NOT NULL REFERENCES scanned_files(id),
  user_email TEXT NOT NULL,
  file_id TEXT NOT NULL, -- Google Drive file ID
  file_name TEXT NOT NULL,
  file_path TEXT NOT NULL, -- Path trong Google Drive
  local_path TEXT, -- Path sau khi download về local
  file_size BIGINT DEFAULT 0,
  mime_type TEXT,
  is_folder BOOLEAN DEFAULT FALSE,
  status TEXT DEFAULT 'pending' CHECK (status IN ('pending', 'downloading', 'completed', 'failed', 'skipped')),
  retry_count INTEGER DEFAULT 0,
  error_message TEXT,
  download_started_at TIMESTAMPTZ,
  download_completed_at TIMESTAMPTZ,
  download_duration INTEGER, -- milliseconds
  created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Update bảng scanned_files để thêm download tracking
ALTER TABLE scanned_files 
ADD COLUMN IF NOT EXISTS download_status TEXT CHECK (download_status IN ('not_downloaded', 'downloaded', 'failed')),
ADD COLUMN IF NOT EXISTS local_path TEXT,
ADD COLUMN IF NOT EXISTS downloaded_at TIMESTAMPTZ;

-- Indexes để tối ưu performance
CREATE INDEX IF NOT EXISTS idx_download_sessions_status ON download_sessions(status);
CREATE INDEX IF NOT EXISTS idx_download_sessions_created_at ON download_sessions(created_at DESC);
CREATE INDEX IF NOT EXISTS idx_download_items_session_id ON download_items(download_session_id);
CREATE INDEX IF NOT EXISTS idx_download_items_status ON download_items(status);
CREATE INDEX IF NOT EXISTS idx_download_items_user_email ON download_items(user_email);
CREATE INDEX IF NOT EXISTS idx_scanned_files_download_status ON scanned_files(download_status);

-- Trigger để auto-update timestamps
CREATE OR REPLACE FUNCTION update_download_sessions_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_update_download_sessions_updated_at
    BEFORE UPDATE ON download_sessions
    FOR EACH ROW
    EXECUTE FUNCTION update_download_sessions_updated_at();

-- View để thống kê download sessions
CREATE OR REPLACE VIEW download_session_stats AS
SELECT 
    ds.id,
    ds.name,
    ds.status,
    ds.total_files,
    ds.downloaded_files,
    ds.failed_files,
    ds.skipped_files,
    ds.total_size,
    ds.downloaded_size,
    CASE 
        WHEN ds.total_files > 0 THEN 
            ROUND((ds.downloaded_files::DECIMAL / ds.total_files::DECIMAL) * 100, 2)
        ELSE 0 
    END as progress_percentage,
    CASE 
        WHEN ds.total_size > 0 THEN 
            ROUND((ds.downloaded_size::DECIMAL / ds.total_size::DECIMAL) * 100, 2)
        ELSE 0 
    END as size_progress_percentage,
    ds.started_at,
    ds.completed_at,
    CASE 
        WHEN ds.completed_at IS NOT NULL AND ds.started_at IS NOT NULL THEN
            EXTRACT(EPOCH FROM (ds.completed_at - ds.started_at))::INTEGER
        WHEN ds.started_at IS NOT NULL THEN
            EXTRACT(EPOCH FROM (NOW() - ds.started_at))::INTEGER
        ELSE NULL
    END as duration_seconds,
    ds.created_at,
    ds.updated_at
FROM download_sessions ds;

-- Enable Row Level Security (RLS) nếu cần
-- ALTER TABLE download_sessions ENABLE ROW LEVEL SECURITY;
-- ALTER TABLE download_items ENABLE ROW LEVEL SECURITY;

-- Realtime subscriptions cho Supabase
-- ALTER PUBLICATION supabase_realtime ADD TABLE download_sessions;
-- ALTER PUBLICATION supabase_realtime ADD TABLE download_items;

COMMENT ON TABLE download_sessions IS 'Quản lý các session download files từ Google Drive';
COMMENT ON TABLE download_items IS 'Track chi tiết từng file trong session download';
COMMENT ON COLUMN download_sessions.selected_users IS 'Array email của users được chọn để download';
COMMENT ON COLUMN download_sessions.download_path IS 'Đường dẫn local để lưu files';
COMMENT ON COLUMN download_sessions.concurrent_downloads IS 'Số lượng download đồng thời';
COMMENT ON COLUMN download_items.local_path IS 'Đường dẫn file sau khi download về local';
COMMENT ON COLUMN download_items.is_folder IS 'True nếu là folder, false nếu là file';
